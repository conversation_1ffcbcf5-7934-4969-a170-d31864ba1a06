package com.stemrobo.humanoid.activities;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.widget.ImageButton;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.ui.StyledPlayerView;
import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.services.VoiceRecognitionService;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;

/**
 * Activity for playing local LMS introduction video in fullscreen
 */
public class LMSVideoPlayerActivity extends AppCompatActivity {
    private static final String TAG = "LMSVideoPlayer";
    
    public static final String EXTRA_VIDEO_PATH = "video_path";
    public static final String EXTRA_VIDEO_TITLE = "video_title";
    
    private ExoPlayer exoPlayer;
    private StyledPlayerView playerView;
    private ImageButton closeButton;
    
    // Broadcast receiver for stop video commands
    private BroadcastReceiver stopVideoReceiver;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        try {
            Log.d(TAG, "LMS Video Player Activity starting...");
            
            // Enable fullscreen mode
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    WindowManager.LayoutParams.FLAG_FULLSCREEN);
            
            // Hide action bar
            if (getSupportActionBar() != null) {
                getSupportActionBar().hide();
            }
            
            setContentView(R.layout.activity_lms_video_player);
            
            initializeViews();
            setupPlayer();
            setupStopVideoReceiver();
            
            // Get video path from intent
            String videoPath = getIntent().getStringExtra(EXTRA_VIDEO_PATH);
            String videoTitle = getIntent().getStringExtra(EXTRA_VIDEO_TITLE);
            
            if (videoPath != null && !videoPath.isEmpty()) {
                playVideo(videoPath);
                Log.d(TAG, "Playing video: " + videoPath);
            } else {
                // Default to LMS intro video
                playVideo("LMS-intro.mp4");
                Log.d(TAG, "Playing default LMS intro video");
            }
            
            Log.d(TAG, "LMS Video Player started for: " + (videoTitle != null ? videoTitle : "LMS Introduction"));
            
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate", e);
            // If there's an error, close the activity gracefully
            Toast.makeText(this, "Error starting video player", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    private void initializeViews() {
        playerView = findViewById(R.id.player_view);
        closeButton = findViewById(R.id.close_button);
        
        // Setup close button
        closeButton.setOnClickListener(v -> {
            Log.d(TAG, "Close button clicked");
            finish();
        });
        
        // Hide system UI for immersive experience
        hideSystemUI();
    }
    
    private void setupPlayer() {
        // Create ExoPlayer instance
        exoPlayer = new ExoPlayer.Builder(this).build();
        
        // Bind player to view
        playerView.setPlayer(exoPlayer);
        
        // Setup player listener
        exoPlayer.addListener(new Player.Listener() {
            @Override
            public void onPlaybackStateChanged(int playbackState) {
                switch (playbackState) {
                    case Player.STATE_READY:
                        Log.d(TAG, "Video ready to play");
                        break;
                    case Player.STATE_ENDED:
                        Log.d(TAG, "Video playback ended");
                        finish(); // Close activity when video ends
                        break;
                    case Player.STATE_BUFFERING:
                        Log.d(TAG, "Video buffering");
                        break;
                }
            }
            
            @Override
            public void onPlayerError(PlaybackException error) {
                Log.e(TAG, "Video playback error: " + error.getMessage());
                Toast.makeText(LMSVideoPlayerActivity.this, 
                    "Error playing video", Toast.LENGTH_SHORT).show();
                finish();
            }
        });
        
        // Auto-play when ready
        exoPlayer.setPlayWhenReady(true);
    }
    
    /**
     * Setup broadcast receiver to listen for stop video commands
     */
    private void setupStopVideoReceiver() {
        stopVideoReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (VoiceRecognitionService.ACTION_STOP_VIDEO_ACTIVITY.equals(intent.getAction())) {
                    Log.d(TAG, "📺 Stop video command received - Closing video player");
                    stopVideoAndClose();
                }
            }
        };
        
        // Register for both local and system broadcasts
        IntentFilter filter = new IntentFilter(VoiceRecognitionService.ACTION_STOP_VIDEO_ACTIVITY);
        LocalBroadcastManager.getInstance(this).registerReceiver(stopVideoReceiver, filter);
        
        // Register system-wide broadcast with proper flag for Android 14+
        if (android.os.Build.VERSION.SDK_INT >= 34) { // API 34 = Android 14 (Upside Down Cake)
            registerReceiver(stopVideoReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            registerReceiver(stopVideoReceiver, filter);
        }
        
        Log.d(TAG, "📺 Stop video receiver registered");
    }
    
    /**
     * Stop video playback and close activity
     */
    private void stopVideoAndClose() {
        try {
            // Stop ExoPlayer playback
            if (exoPlayer != null) {
                exoPlayer.stop();
                exoPlayer.setPlayWhenReady(false);
                Log.d(TAG, "📺 ExoPlayer stopped");
            }
            
            // Close activity on UI thread
            runOnUiThread(() -> {
                Log.d(TAG, "📺 Closing video player activity");
                finish();
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error stopping video and closing activity", e);
            // Ensure we still close the activity even if there's an error
            runOnUiThread(this::finish);
        }
    }
    
    private void playVideo(String videoPath) {
        try {
            Log.d(TAG, "Attempting to play video: " + videoPath);
            
            // Method 1: Try direct asset URI
            Uri videoUri = null;
            
            if (videoPath.equals("LMS-intro.mp4")) {
                // Try multiple URI formats for assets
                String[] uriFormats = {
                    "asset:///" + videoPath,
                    "file:///android_asset/" + videoPath,
                    "android.resource://" + getPackageName() + "/raw/lms_intro"
                };
                
                for (String uriFormat : uriFormats) {
                    try {
                        videoUri = Uri.parse(uriFormat);
                        Log.d(TAG, "Trying URI format: " + uriFormat);
                        
                        // Create media item and test
                        MediaItem mediaItem = MediaItem.fromUri(videoUri);
                        exoPlayer.setMediaItem(mediaItem);
                        exoPlayer.prepare();
                        
                        Log.d(TAG, "✅ Successfully loaded video with URI: " + uriFormat);
                        return; // Success, exit method
                        
                    } catch (Exception e) {
                        Log.w(TAG, "Failed with URI format: " + uriFormat + ", error: " + e.getMessage());
                        continue; // Try next format
                    }
                }
                
                // If all URI formats failed, try copying asset to cache
                Log.d(TAG, "All URI formats failed, trying to copy asset to cache...");
                copyAssetToCache(videoPath);
                return;
            }
            
            // Fallback for other video paths
            videoUri = Uri.parse("asset:///" + videoPath);
            MediaItem mediaItem = MediaItem.fromUri(videoUri);
            exoPlayer.setMediaItem(mediaItem);
            exoPlayer.prepare();
            
            Log.d(TAG, "Playing video from assets: " + videoPath);
            
        } catch (Exception e) {
            Log.e(TAG, "Error setting up video playback", e);
            Toast.makeText(this, "Error loading video", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    /**
     * Copy asset file to cache directory and play from there
     */
    private void copyAssetToCache(String assetFileName) {
        try {
            Log.d(TAG, "Copying asset to cache: " + assetFileName);
            
            // Create cache file
            File cacheDir = getCacheDir();
            File videoFile = new File(cacheDir, assetFileName);
            
            // Check if file already exists in cache
            if (videoFile.exists()) {
                Log.d(TAG, "Video file already exists in cache, playing directly");
                playVideoFromFile(videoFile);
                return;
            }
            
            // Copy asset to cache
            try (InputStream inputStream = getAssets().open(assetFileName);
                 FileOutputStream outputStream = new FileOutputStream(videoFile)) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytes = 0;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }
                
                Log.d(TAG, "Successfully copied " + totalBytes + " bytes to cache");
                
                // Play video from cache file
                playVideoFromFile(videoFile);
                
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Error copying asset to cache", e);
            Toast.makeText(this, "Error loading video file", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    /**
     * Play video from a local file
     */
    private void playVideoFromFile(File videoFile) {
        try {
            Uri videoUri = Uri.fromFile(videoFile);
            Log.d(TAG, "Playing video from file: " + videoUri.toString());
            
            MediaItem mediaItem = MediaItem.fromUri(videoUri);
            exoPlayer.setMediaItem(mediaItem);
            exoPlayer.prepare();
            
            Log.d(TAG, "✅ Successfully loaded video from file");
            
        } catch (Exception e) {
            Log.e(TAG, "Error playing video from file", e);
            Toast.makeText(this, "Error playing video", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    private void hideSystemUI() {
        // Enable immersive fullscreen mode
        View decorView = getWindow().getDecorView();
        decorView.setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_FULLSCREEN
        );
    }
    
    @Override
    protected void onStart() {
        super.onStart();
        if (exoPlayer != null) {
            exoPlayer.setPlayWhenReady(true);
        }
    }
    
    @Override
    protected void onStop() {
        super.onStop();
        if (exoPlayer != null) {
            exoPlayer.setPlayWhenReady(false);
        }
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // Unregister broadcast receiver
        if (stopVideoReceiver != null) {
            try {
                LocalBroadcastManager.getInstance(this).unregisterReceiver(stopVideoReceiver);
                unregisterReceiver(stopVideoReceiver);
                Log.d(TAG, "📺 Stop video receiver unregistered");
            } catch (Exception e) {
                Log.w(TAG, "Error unregistering stop video receiver: " + e.getMessage());
            }
        }
        
        if (exoPlayer != null) {
            exoPlayer.release();
            exoPlayer = null;
        }
        Log.d(TAG, "LMS Video Player destroyed");
    }
    
    @Override
    public void onBackPressed() {
        // Allow back button to close video
        super.onBackPressed();
    }
}
