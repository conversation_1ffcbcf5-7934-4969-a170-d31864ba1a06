package com.stemrobo.humanoid.activities;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.WindowManager;
import android.webkit.WebChromeClient;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageButton;
import android.widget.ProgressBar;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.services.VoiceRecognitionService;

/**
 * Activity for playing YouTube LMS class videos in fullscreen
 */
public class LMSYouTubePlayerActivity extends AppCompatActivity {
    private static final String TAG = "LMSYouTubePlayer";
    
    public static final String EXTRA_YOUTUBE_URL = "youtube_url";
    public static final String EXTRA_CLASS_NUMBER = "class_number";
    
    // Default YouTube video for all classes (will be customized later)
    private static final String DEFAULT_YOUTUBE_URL = "https://www.youtube.com/watch?v=8a2HGrk7MhM";
    
    private WebView webView;
    private ImageButton closeButton;
    private ProgressBar progressBar;
    
    // Broadcast receiver for stop video commands
    private BroadcastReceiver stopVideoReceiver;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        try {
            Log.d(TAG, "LMS YouTube Player Activity starting...");
            
            // Enable fullscreen mode
            getWindow().setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                    WindowManager.LayoutParams.FLAG_FULLSCREEN);
            
            // Hide action bar
            if (getSupportActionBar() != null) {
                getSupportActionBar().hide();
            }
            
            setContentView(R.layout.activity_lms_youtube_player);
            
            initializeViews();
            setupWebView();
            setupStopVideoReceiver();
            
            // Get YouTube URL and class number from intent
            String youtubeUrl = getIntent().getStringExtra(EXTRA_YOUTUBE_URL);
            int classNumber = getIntent().getIntExtra(EXTRA_CLASS_NUMBER, 0);
            
            Log.d(TAG, "Intent extras - YouTube URL: " + youtubeUrl + ", Class Number: " + classNumber);
            
            if (youtubeUrl != null && !youtubeUrl.isEmpty()) {
                loadYouTubeVideo(youtubeUrl);
                Log.d(TAG, "Loading specified YouTube URL: " + youtubeUrl);
            } else {
                // Use default URL for the specified class
                String defaultUrl = getYouTubeUrlForClass(classNumber);
                loadYouTubeVideo(defaultUrl);
                Log.d(TAG, "Loading default URL for class " + classNumber + ": " + defaultUrl);
            }
            
            Log.d(TAG, "LMS YouTube Player started for class: " + classNumber);
            
        } catch (Exception e) {
            Log.e(TAG, "Error in onCreate", e);
            // If there's an error, close the activity gracefully
            Toast.makeText(this, "Error starting YouTube player", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    private void initializeViews() {
        webView = findViewById(R.id.youtube_webview);
        closeButton = findViewById(R.id.close_button);
        progressBar = findViewById(R.id.progress_bar);
        
        // Setup close button
        closeButton.setOnClickListener(v -> {
            Log.d(TAG, "Close button clicked");
            finish();
        });
        
        // Hide system UI for immersive experience
        hideSystemUI();
    }
    
    private void setupWebView() {
        try {
            WebSettings webSettings = webView.getSettings();
            
            // Enable JavaScript (required for YouTube)
            webSettings.setJavaScriptEnabled(true);
            
            // Enable DOM storage
            webSettings.setDomStorageEnabled(true);
            
            // Enable media playback
            webSettings.setMediaPlaybackRequiresUserGesture(false);
            
            // Allow mixed content (HTTP/HTTPS)
            webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
            
            // Set user agent to mobile for better YouTube experience
            webSettings.setUserAgentString("Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.120 Mobile Safari/537.36");
            
            Log.d(TAG, "WebView settings configured successfully");
            
            // Setup WebView client
            webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, android.graphics.Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                progressBar.setVisibility(View.VISIBLE);
                Log.d(TAG, "Loading YouTube video: " + url);
            }
            
            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                progressBar.setVisibility(View.GONE);
                Log.d(TAG, "YouTube video loaded successfully");
            }
            
            @Override
            public void onReceivedError(WebView view, int errorCode, String description, String failingUrl) {
                super.onReceivedError(view, errorCode, description, failingUrl);
                Log.e(TAG, "WebView error: " + description);
                progressBar.setVisibility(View.GONE);
                Toast.makeText(LMSYouTubePlayerActivity.this, 
                    "Error loading video", Toast.LENGTH_SHORT).show();
            }
        });
        
        // Setup WebChrome client for fullscreen video support
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                progressBar.setProgress(newProgress);
            }
        });
            
        Log.d(TAG, "WebView setup completed successfully");
            
    } catch (Exception e) {
        Log.e(TAG, "Error setting up WebView", e);
        Toast.makeText(this, "Error initializing video player", Toast.LENGTH_SHORT).show();
        finish();
    }
}
    
    /**
     * Setup broadcast receiver to listen for stop video commands
     */
    private void setupStopVideoReceiver() {
        stopVideoReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (VoiceRecognitionService.ACTION_STOP_VIDEO_ACTIVITY.equals(intent.getAction())) {
                    Log.d(TAG, "📺 Stop video command received - Closing YouTube player");
                    stopVideoAndClose();
                }
            }
        };
        
        // Register for both local and system broadcasts
        IntentFilter filter = new IntentFilter(VoiceRecognitionService.ACTION_STOP_VIDEO_ACTIVITY);
        LocalBroadcastManager.getInstance(this).registerReceiver(stopVideoReceiver, filter);
        
        // Register system-wide broadcast with proper flag for Android 14+
        if (android.os.Build.VERSION.SDK_INT >= 34) { // API 34 = Android 14 (Upside Down Cake)
            registerReceiver(stopVideoReceiver, filter, Context.RECEIVER_NOT_EXPORTED);
        } else {
            registerReceiver(stopVideoReceiver, filter);
        }
        
        Log.d(TAG, "📺 Stop video receiver registered");
    }
    
    /**
     * Stop video playback and close activity
     */
    private void stopVideoAndClose() {
        try {
            // Stop WebView loading and playback
            if (webView != null) {
                webView.stopLoading();
                webView.loadUrl("about:blank");
                Log.d(TAG, "📺 WebView stopped and cleared");
            }
            
            // Close activity on UI thread
            runOnUiThread(() -> {
                Log.d(TAG, "📺 Closing YouTube player activity");
                finish();
            });
            
        } catch (Exception e) {
            Log.e(TAG, "Error stopping video and closing activity", e);
            // Ensure we still close the activity even if there's an error
            runOnUiThread(this::finish);
        }
    }
    
    private void loadYouTubeVideo(String youtubeUrl) {
        try {
            // Convert YouTube watch URL to embed URL for better mobile experience
            String embedUrl = convertToEmbedUrl(youtubeUrl);
            
            // Create HTML content with responsive YouTube embed
            String htmlContent = createYouTubeEmbedHtml(embedUrl);
            
            // Load the HTML content
            webView.loadDataWithBaseURL("https://www.youtube.com", htmlContent, "text/html", "UTF-8", null);
            
            Log.d(TAG, "Loading YouTube embed: " + embedUrl);
            
        } catch (Exception e) {
            Log.e(TAG, "Error loading YouTube video", e);
            Toast.makeText(this, "Error loading video", Toast.LENGTH_SHORT).show();
            finish();
        }
    }
    
    private String convertToEmbedUrl(String youtubeUrl) {
        // Extract video ID from YouTube URL
        String videoId = "";
        
        if (youtubeUrl.contains("watch?v=")) {
            videoId = youtubeUrl.split("watch\\?v=")[1];
            if (videoId.contains("&")) {
                videoId = videoId.split("&")[0];
            }
        } else if (youtubeUrl.contains("youtu.be/")) {
            videoId = youtubeUrl.split("youtu.be/")[1];
            if (videoId.contains("?")) {
                videoId = videoId.split("\\?")[0];
            }
        }
        
        return "https://www.youtube.com/embed/" + videoId + "?autoplay=1&fs=1&rel=0";
    }
    
    private String createYouTubeEmbedHtml(String embedUrl) {
        return "<!DOCTYPE html>" +
                "<html>" +
                "<head>" +
                "<meta name='viewport' content='width=device-width, initial-scale=1.0'>" +
                "<style>" +
                "body { margin: 0; padding: 0; background: black; }" +
                "iframe { width: 100vw; height: 100vh; border: none; }" +
                "</style>" +
                "</head>" +
                "<body>" +
                "<iframe src='" + embedUrl + "' " +
                "allowfullscreen='true' " +
                "allow='autoplay; encrypted-media'>" +
                "</iframe>" +
                "</body>" +
                "</html>";
    }
    
    private String getYouTubeUrlForClass(int classNumber) {
        // For now, return the same URL for all classes
        // This can be customized later with specific URLs for each class
        switch (classNumber) {
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case 9:
            case 10:
            case 11:
            case 12:
            default:
                return DEFAULT_YOUTUBE_URL;
        }
    }
    
    private void hideSystemUI() {
        // Enable immersive fullscreen mode
        View decorView = getWindow().getDecorView();
        decorView.setSystemUiVisibility(
            View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
            | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
            | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            | View.SYSTEM_UI_FLAG_FULLSCREEN
        );
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // Unregister broadcast receiver
        if (stopVideoReceiver != null) {
            try {
                LocalBroadcastManager.getInstance(this).unregisterReceiver(stopVideoReceiver);
                unregisterReceiver(stopVideoReceiver);
                Log.d(TAG, "📺 Stop video receiver unregistered");
            } catch (Exception e) {
                Log.w(TAG, "Error unregistering stop video receiver: " + e.getMessage());
            }
        }
        
        if (webView != null) {
            webView.destroy();
        }
        Log.d(TAG, "LMS YouTube Player destroyed");
    }
    
    @Override
    public void onBackPressed() {
        if (webView != null && webView.canGoBack()) {
            webView.goBack();
        } else {
            super.onBackPressed();
        }
    }
}
