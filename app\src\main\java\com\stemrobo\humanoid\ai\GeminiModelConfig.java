package com.stemrobo.humanoid.ai;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;

/**
 * Configuration class for managing multiple Gemini AI models
 * Supports switching between different Gemini models with separate API keys
 */
public class GeminiModelConfig {
    private static final String TAG = "GeminiModelConfig";
    
    // Preference keys
    public static final String PREF_CURRENT_MODEL = "gemini_current_model";
    public static final String PREF_MODEL1_NAME = "gemini_model1_name";
    public static final String PREF_MODEL1_API_KEY = "gemini_model1_api_key";
    public static final String PREF_MODEL1_ENDPOINT = "gemini_model1_endpoint";
    public static final String PREF_MODEL2_NAME = "gemini_model2_name";
    public static final String PREF_MODEL2_API_KEY = "gemini_model2_api_key";
    public static final String PREF_MODEL2_ENDPOINT = "gemini_model2_endpoint";
    
    // Default model configurations
    public static final String DEFAULT_MODEL1_NAME = "Gemini 2.0 Flash";
    public static final String DEFAULT_MODEL1_API_KEY = "AIzaSyASknGS2TkzUolrfAjYSMTh2OY3OBrmBbc";
    public static final String DEFAULT_MODEL1_ENDPOINT = "gemini-2.5-flash-lite:generateContent";
    
    public static final String DEFAULT_MODEL2_NAME = "Gemini 1.5 Pro";
    public static final String DEFAULT_MODEL2_API_KEY = "AIzaSyASknGS2TkzUolrfAjYSMTh2OY3OBrmBbc";
    public static final String DEFAULT_MODEL2_ENDPOINT = "gemma-3-27b-it:generateContent";
    
    // Model identifiers
    public static final int MODEL_1 = 1;
    public static final int MODEL_2 = 2;
    
    // Default current model
    public static final int DEFAULT_CURRENT_MODEL = MODEL_1;
    
    private final SharedPreferences preferences;
    private int currentModel;
    
    public GeminiModelConfig(Context context) {
        this.preferences = PreferenceManager.getDefaultSharedPreferences(context);
        loadCurrentModel();
    }
    
    /**
     * Load current model from preferences
     */
    private void loadCurrentModel() {
        currentModel = preferences.getInt(PREF_CURRENT_MODEL, DEFAULT_CURRENT_MODEL);
    }
    
    /**
     * Get current active model
     */
    public int getCurrentModel() {
        return currentModel;
    }
    
    /**
     * Set current active model
     */
    public void setCurrentModel(int model) {
        if (model == MODEL_1 || model == MODEL_2) {
            this.currentModel = model;
            preferences.edit().putInt(PREF_CURRENT_MODEL, model).apply();
            android.util.Log.d(TAG, "Switched to model: " + getModelName(model));
        }
    }
    
    /**
     * Get API key for specified model
     */
    public String getApiKey(int model) {
        switch (model) {
            case MODEL_1:
                return preferences.getString(PREF_MODEL1_API_KEY, DEFAULT_MODEL1_API_KEY);
            case MODEL_2:
                return preferences.getString(PREF_MODEL2_API_KEY, DEFAULT_MODEL2_API_KEY);
            default:
                return DEFAULT_MODEL1_API_KEY;
        }
    }
    
    /**
     * Get API key for current model
     */
    public String getCurrentApiKey() {
        return getApiKey(currentModel);
    }
    
    /**
     * Get endpoint for specified model
     */
    public String getEndpoint(int model) {
        switch (model) {
            case MODEL_1:
                return preferences.getString(PREF_MODEL1_ENDPOINT, DEFAULT_MODEL1_ENDPOINT);
            case MODEL_2:
                return preferences.getString(PREF_MODEL2_ENDPOINT, DEFAULT_MODEL2_ENDPOINT);
            default:
                return DEFAULT_MODEL1_ENDPOINT;
        }
    }
    
    /**
     * Get endpoint for current model
     */
    public String getCurrentEndpoint() {
        return getEndpoint(currentModel);
    }
    
    /**
     * Get full API URL for specified model
     */
    public String getApiUrl(int model) {
        return "https://generativelanguage.googleapis.com/v1beta/models/" + getEndpoint(model);
    }
    
    /**
     * Get full API URL for current model
     */
    public String getCurrentApiUrl() {
        return getApiUrl(currentModel);
    }
    
    /**
     * Get model name for specified model
     */
    public String getModelName(int model) {
        switch (model) {
            case MODEL_1:
                return preferences.getString(PREF_MODEL1_NAME, DEFAULT_MODEL1_NAME);
            case MODEL_2:
                return preferences.getString(PREF_MODEL2_NAME, DEFAULT_MODEL2_NAME);
            default:
                return DEFAULT_MODEL1_NAME;
        }
    }
    
    /**
     * Get current model name
     */
    public String getCurrentModelName() {
        return getModelName(currentModel);
    }
    
    /**
     * Set configuration for Model 1
     */
    public void setModel1Config(String name, String apiKey, String endpoint) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(PREF_MODEL1_NAME, name);
        editor.putString(PREF_MODEL1_API_KEY, apiKey);
        editor.putString(PREF_MODEL1_ENDPOINT, endpoint);
        editor.apply();
        android.util.Log.d(TAG, "Model 1 configuration updated: " + name);
    }
    
    /**
     * Set configuration for Model 2
     */
    public void setModel2Config(String name, String apiKey, String endpoint) {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putString(PREF_MODEL2_NAME, name);
        editor.putString(PREF_MODEL2_API_KEY, apiKey);
        editor.putString(PREF_MODEL2_ENDPOINT, endpoint);
        editor.apply();
        android.util.Log.d(TAG, "Model 2 configuration updated: " + name);
    }
    
    /**
     * Get available model names as array
     */
    public String[] getAvailableModelNames() {
        return new String[] {
            getModelName(MODEL_1),
            getModelName(MODEL_2)
        };
    }
    
    /**
     * Get model ID from name
     */
    public int getModelIdFromName(String name) {
        if (name.equals(getModelName(MODEL_1))) {
            return MODEL_1;
        } else if (name.equals(getModelName(MODEL_2))) {
            return MODEL_2;
        }
        return MODEL_1; // Default to Model 1
    }
    
    /**
     * Check if API key is configured for specified model
     */
    public boolean isApiKeyConfigured(int model) {
        String apiKey = getApiKey(model);
        return apiKey != null && !apiKey.trim().isEmpty() && 
               !apiKey.equals("YOUR_API_KEY_HERE") && !apiKey.equals("ENTER_YOUR_API_KEY");
    }
    
    /**
     * Check if current model has valid API key
     */
    public boolean isCurrentModelConfigured() {
        return isApiKeyConfigured(currentModel);
    }
    
    /**
     * Get model configuration summary
     */
    public String getConfigurationSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Current Model: ").append(getCurrentModelName()).append("\n");
        summary.append("Model 1: ").append(getModelName(MODEL_1));
        summary.append(isApiKeyConfigured(MODEL_1) ? " ✓" : " ✗").append("\n");
        summary.append("Model 2: ").append(getModelName(MODEL_2));
        summary.append(isApiKeyConfigured(MODEL_2) ? " ✓" : " ✗");
        return summary.toString();
    }
    
    /**
     * Reset to default configuration
     */
    public void resetToDefaults() {
        SharedPreferences.Editor editor = preferences.edit();
        editor.putInt(PREF_CURRENT_MODEL, DEFAULT_CURRENT_MODEL);
        editor.putString(PREF_MODEL1_NAME, DEFAULT_MODEL1_NAME);
        editor.putString(PREF_MODEL1_API_KEY, DEFAULT_MODEL1_API_KEY);
        editor.putString(PREF_MODEL1_ENDPOINT, DEFAULT_MODEL1_ENDPOINT);
        editor.putString(PREF_MODEL2_NAME, DEFAULT_MODEL2_NAME);
        editor.putString(PREF_MODEL2_API_KEY, DEFAULT_MODEL2_API_KEY);
        editor.putString(PREF_MODEL2_ENDPOINT, DEFAULT_MODEL2_ENDPOINT);
        editor.apply();
        
        loadCurrentModel();
        android.util.Log.d(TAG, "Configuration reset to defaults");
    }
}