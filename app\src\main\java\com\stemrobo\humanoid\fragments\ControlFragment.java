package com.stemrobo.humanoid.fragments;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.preference.PreferenceManager;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.stemrobo.humanoid.MainActivity;
import com.stemrobo.humanoid.R;
import com.stemrobo.humanoid.communication.ESP32CommunicationManager;
import com.stemrobo.humanoid.models.RobotCommand;
import com.stemrobo.humanoid.models.RobotResponse;

import java.util.HashMap;
import java.util.Map;

public class ControlFragment extends Fragment implements ESP32CommunicationManager.CommunicationListener {
    private static final String TAG = "ControlFragment";

    private ESP32CommunicationManager communicationManager;



    // Real-time distance monitoring
    private Handler distanceUpdateHandler;
    private Runnable distanceUpdateRunnable;
    private boolean isRealTimeMonitoringActive = false;
    private static final int DISTANCE_UPDATE_INTERVAL = 500; // Update every 500ms
    
    // Emergency and movement controls
    private Button btnEmergencyStop;
    private Button btnForward, btnBackward, btnLeft, btnRight, btnStop;


    
    // Arm controls
    private SeekBar leftShoulderSeekBar, rightShoulderSeekBar;
    private TextView leftShoulderValue, rightShoulderValue;
    private Button btnWave, btnPoint, btnRest;
    
    // Head controls
    private SeekBar headPanSeekBar, headTiltSeekBar;
    private TextView headPanValue, headTiltValue;
    private Button btnCenterHead, btnLookLeft, btnLookRight;

    // Ultrasonic Distance Display (for Smart Greeting integration)
    private TextView ultrasonicDistanceValue;
    private Button btnGetDistance;

    // Movement Speed Control
    private SeekBar movementSpeedSeekBar;
    private TextView movementSpeedValue;

    // Removed speed control - using digital motor control only
    
    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, 
                           @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_control, container, false);
    }
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initializeViews(view);
        setupListeners();



        // Get communication manager from MainActivity
        if (getActivity() instanceof MainActivity) {
            communicationManager = ((MainActivity) getActivity()).getCommunicationManager();

            // Set this fragment as the communication listener to receive distance responses
            if (communicationManager != null) {
                communicationManager.setCommunicationListener(this);

                // Debug USB connection status
                debugUSBConnection();
            }
        }

        Log.d(TAG, "ControlFragment initialized");
    }

    private void setupRobotTypeReceiver() {
        // Robot type receiver removed - using normal two-wheel robot only
    }

    private void initializeViews(View view) {
        // Emergency stop control
        btnEmergencyStop = view.findViewById(R.id.btn_emergency_stop);

        // Movement controls
        btnForward = view.findViewById(R.id.btn_forward);
        btnBackward = view.findViewById(R.id.btn_backward);
        btnLeft = view.findViewById(R.id.btn_left);
        btnRight = view.findViewById(R.id.btn_right);
        btnStop = view.findViewById(R.id.btn_stop);



        // Speed control removed - using digital motor control
        
        // Arm controls
        leftShoulderSeekBar = view.findViewById(R.id.left_shoulder_seekbar);
        rightShoulderSeekBar = view.findViewById(R.id.right_shoulder_seekbar);
        leftShoulderValue = view.findViewById(R.id.left_shoulder_value);
        rightShoulderValue = view.findViewById(R.id.right_shoulder_value);
        btnWave = view.findViewById(R.id.btn_wave);
        btnPoint = view.findViewById(R.id.btn_point);
        btnRest = view.findViewById(R.id.btn_rest);
        
        // Head controls
        headPanSeekBar = view.findViewById(R.id.head_pan_seekbar);
        headTiltSeekBar = view.findViewById(R.id.head_tilt_seekbar);
        headPanValue = view.findViewById(R.id.head_pan_value);
        headTiltValue = view.findViewById(R.id.head_tilt_value);
        btnCenterHead = view.findViewById(R.id.btn_center_head);
        btnLookLeft = view.findViewById(R.id.btn_look_left);
        btnLookRight = view.findViewById(R.id.btn_look_right);

        // Ultrasonic Distance Display (for Smart Greeting integration)
        ultrasonicDistanceValue = view.findViewById(R.id.ultrasonic_distance_value);
        btnGetDistance = view.findViewById(R.id.btn_get_distance);

        // Movement Speed Control
        movementSpeedSeekBar = view.findViewById(R.id.movement_speed_seekbar);
        movementSpeedValue = view.findViewById(R.id.movement_speed_value);

        // Add click listener for distance measurement
        btnGetDistance.setOnClickListener(v -> getUltrasonicDistance());

        // Set initial values for servos (0-180° range)
        leftShoulderSeekBar.setProgress(0); // Rest position for left arm (0° slider = 180° servo via reflection)
        rightShoulderSeekBar.setProgress(0); // Rest position for right arm (0° slider = 0° servo direct)
        headPanSeekBar.setProgress(90);
        headTiltSeekBar.setProgress(90);
    }
    
    private void setupListeners() {
        // Emergency stop button listener
        btnEmergencyStop.setOnClickListener(v -> emergencyStopAll());

        // Movement button listeners - using touch listeners for momentary control
        setupMomentaryButton(btnForward, "forward");
        setupMomentaryButton(btnBackward, "backward");
        setupMomentaryButton(btnLeft, "turn_left");
        setupMomentaryButton(btnRight, "turn_right");
        btnStop.setOnClickListener(v -> sendMovementCommand("stop"));



        // Speed control removed - using digital motor control
        
        // Arm control listeners
        leftShoulderSeekBar.setOnSeekBarChangeListener(createServoListener("left_shoulder", leftShoulderValue));
        rightShoulderSeekBar.setOnSeekBarChangeListener(createServoListener("right_shoulder", rightShoulderValue));
        
        // Gesture buttons
        btnWave.setOnClickListener(v -> sendGestureCommand("wave"));
        btnPoint.setOnClickListener(v -> sendGestureCommand("point"));
        btnRest.setOnClickListener(v -> sendGestureCommand("rest"));
        
        // Head control listeners
        headPanSeekBar.setOnSeekBarChangeListener(createServoListener("head_pan", headPanValue));
        headTiltSeekBar.setOnSeekBarChangeListener(createServoListener("head_tilt", headTiltValue));
        btnCenterHead.setOnClickListener(v -> centerHead());
        btnLookLeft.setOnClickListener(v -> sendHeadCommand("LOOK_LEFT"));
        btnLookRight.setOnClickListener(v -> sendHeadCommand("LOOK_RIGHT"));

        // Ultrasonic Distance control listener (for Smart Greeting integration)
        btnGetDistance.setOnClickListener(v -> getUltrasonicDistance());

        // Movement Speed control listener
        movementSpeedSeekBar.setOnSeekBarChangeListener(createSpeedListener());

        // Initialize real-time distance monitoring
        initializeRealTimeMonitoring();

        // Initialize speed control with default value (15ms)
        movementSpeedSeekBar.setProgress(10); // 10 + 5 = 15ms
        movementSpeedValue.setText("15ms");
    }

    /**
     * Setup momentary button behavior for movement controls
     * Button sends movement command on press, stop command on release
     */
    private void setupMomentaryButton(Button button, String direction) {
        button.setOnTouchListener((v, event) -> {
            switch (event.getAction()) {
                case android.view.MotionEvent.ACTION_DOWN:
                    // Button pressed - start movement
                    sendMovementCommand(direction);
                    v.setPressed(true);
                    return true;

                case android.view.MotionEvent.ACTION_UP:
                case android.view.MotionEvent.ACTION_CANCEL:
                    // Button released - stop movement
                    sendMovementCommand("stop");
                    v.setPressed(false);
                    v.performClick(); // For accessibility
                    return true;

                default:
                    return false;
            }
        });
    }
    
    private SeekBar.OnSeekBarChangeListener createServoListener(String servoName, TextView valueText) {
        return new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                valueText.setText(progress + "°");
                if (fromUser) {
                    sendServoCommand(servoName, progress);
                }
            }
            
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        };
    }

    private SeekBar.OnSeekBarChangeListener createSpeedListener() {
        return new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser) {
                    // Convert progress (0-45) to delay (5-50ms)
                    int delayMs = progress + 5;
                    movementSpeedValue.setText(delayMs + "ms");
                    sendSpeedCommand(delayMs);
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        };
    }

    private void sendMovementCommand(String direction) {
        if (communicationManager == null) {
            showError("Communication manager not available");
            return;
        }

        // Two-wheel robot motor command mapping for ESP32
        String esp32Command;

        switch (direction) {
            case "forward":
                esp32Command = "F";
                break;
            case "backward":
                esp32Command = "B";
                break;
            case "turn_left":
                esp32Command = "L";
                break;
            case "turn_right":
                esp32Command = "R";
                break;
            case "stop":
                esp32Command = "S";
                break;
            default:
                esp32Command = "S"; // Default to stop for unknown commands
                break;
        }

        communicationManager.sendMotorCommand(esp32Command);
        android.util.Log.d(TAG, "Sent movement command: " + direction + " -> " + esp32Command);
    }
    
    private void sendServoCommand(String servoName, int angle) {
        if (communicationManager == null) {
            showError("Communication manager not available");
            return;
        }

        // Apply reflection for left hand: when slider shows 10°, execute 170° (180° - slider_value)
        int actualAngle = angle;
        if ("left_shoulder".equals(servoName)) {
            actualAngle = 180 - angle; // Left hand reflection: 0° → 180°, 10° → 170°, 180° → 0°
            Log.d(TAG, "Left hand reflection: slider " + angle + "° → servo " + actualAngle + "°");
        }
        // Right hand uses direct mapping (working correctly)

        Map<String, Object> parameters = new HashMap<>();
        parameters.put("servo", servoName);
        parameters.put("angle", actualAngle);

        communicationManager.sendServoCommand("set_angle", parameters);
        Log.d(TAG, "Sent servo command: " + servoName + " slider:" + angle + "° → actual:" + actualAngle + "°");
    }
    
    private void sendGestureCommand(String gesture) {
        if (communicationManager == null) {
            showError("Communication manager not available");
            return;
        }

        // Convert gesture to ESP32 command format
        String esp32Command;
        switch (gesture) {
            case "wave":
                esp32Command = "WAVE";
                break;
            case "point":
                esp32Command = "POINT";
                break;
            case "rest":
                esp32Command = "REST";
                break;
            default:
                esp32Command = "REST"; // Default to rest position
                break;
        }

        communicationManager.sendServoCommand(esp32Command);
        Log.d(TAG, "Sent gesture command: " + gesture + " -> " + esp32Command);
    }
    
    private void centerHead() {
        headPanSeekBar.setProgress(90);
        headTiltSeekBar.setProgress(90);
        sendServoCommand("head_pan", 90);
        sendServoCommand("head_tilt", 90);
    }

    private void sendHeadCommand(String command) {
        if (communicationManager == null) {
            showError("Communication manager not available");
            return;
        }

        communicationManager.sendServoCommand(command);
        Log.d(TAG, "Sent head command: " + command);

        // Update UI based on command
        if ("LOOK_LEFT".equals(command)) {
            headPanSeekBar.setProgress(45); // HEAD_LOOK_LEFT_POSITION
            headPanValue.setText("45°");
        } else if ("LOOK_RIGHT".equals(command)) {
            headPanSeekBar.setProgress(135); // HEAD_LOOK_RIGHT_POSITION
            headPanValue.setText("135°");
        } else if ("CENTER_HEAD".equals(command)) {
            headPanSeekBar.setProgress(90);
            headTiltSeekBar.setProgress(90);
            headPanValue.setText("90°");
            headTiltValue.setText("90°");
        }
    }

    private void sendSpeedCommand(int delayMs) {
        if (communicationManager == null) {
            showError("Communication manager not available");
            return;
        }

        String command = "SET_SERVO_SPEED:" + delayMs;
        communicationManager.sendServoCommand(command);
        Log.d(TAG, "Sent speed command: " + command);
    }

    private void emergencyStopAll() {
        if (communicationManager == null) {
            showError("Communication manager not available");
            return;
        }

        Log.d(TAG, "EMERGENCY STOP ACTIVATED");

        // Send emergency stop command to ESP32
        communicationManager.emergencyStopAll();

        // Reset all UI controls to safe positions
        resetAllControls();

        // Show confirmation to user
        if (getContext() != null) {
            Toast.makeText(getContext(), "🛑 EMERGENCY STOP - All operations halted", Toast.LENGTH_LONG).show();
        }
    }

    private void resetAllControls() {
        // Reset servo controls to rest positions (0-180° range with left hand reflection)
        leftShoulderSeekBar.setProgress(0); // Rest position for left arm (0° slider = 180° servo via reflection)
        rightShoulderSeekBar.setProgress(0); // Rest position for right arm (0° slider = 0° servo direct)
        headPanSeekBar.setProgress(90);
        headTiltSeekBar.setProgress(90);

        // Update value displays to match slider positions
        leftShoulderValue.setText("0°");  // Left arm slider position (reflects to 180° servo)
        rightShoulderValue.setText("0°"); // Right arm slider position (direct to 0° servo)
        headPanValue.setText("90°");
        headTiltValue.setText("90°");

        Log.d(TAG, "All controls reset to safe positions - Left: 0° slider (180° servo), Right: 0° slider (0° servo), Head: 90°");
    }

    // Ultrasonic Distance Sensor Method (for Smart Greeting integration)
    private void getUltrasonicDistance() {
        if (communicationManager == null) {
            showError("Communication manager not available");
            return;
        }

        communicationManager.getUltrasonicDistance();
        System.out.println(TAG + ": Requested ultrasonic distance measurement");

        // Update UI to show we're getting distance
        ultrasonicDistanceValue.setText("Measuring...");
    }

    private void showError(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
        Log.e(TAG, message);
    }

    // Real-time Distance Monitoring Methods
    private void initializeRealTimeMonitoring() {
        distanceUpdateHandler = new Handler(Looper.getMainLooper());

        distanceUpdateRunnable = new Runnable() {
            @Override
            public void run() {
                if (isRealTimeMonitoringActive && communicationManager != null) {
                    // Check and ensure USB connection before requesting distance
                    ensureUSBConnection();

                    // Request distance reading from ESP32
                    communicationManager.getUltrasonicDistance();

                    // Schedule next update
                    distanceUpdateHandler.postDelayed(this, DISTANCE_UPDATE_INTERVAL);
                }
            }
        };

        // Start real-time monitoring automatically
        startRealTimeMonitoring();
    }

    // Debug USB connection status
    private void debugUSBConnection() {
        if (communicationManager == null) {
            Log.w(TAG, "Communication manager is null - cannot debug USB");
            return;
        }

        try {
            Log.d(TAG, "=== USB CONNECTION DEBUG ===");
            Log.d(TAG, "USB Connected: " + communicationManager.isUSBConnected());
            Log.d(TAG, "Current Mode: " + communicationManager.getCurrentMode());
            Log.d(TAG, "Connection Mode Status: " + communicationManager.getAllConnectionModes());

            // Try to get detailed USB debug info if available
            String usbDebugInfo = communicationManager.getUSBDebugInfo();
            Log.d(TAG, usbDebugInfo);

            // If USB is not connected, try to scan for devices
            if (!communicationManager.isUSBConnected()) {
                Log.d(TAG, "USB not connected - attempting scan...");
                communicationManager.scanForUSBDevices();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error debugging USB connection: " + e.getMessage());
        }
    }



    // Ensure USB connection is active for real-time monitoring
    private void ensureUSBConnection() {
        if (communicationManager == null) {
            return;
        }

        try {
            // Check if USB is connected
            if (!communicationManager.isUSBConnected()) {
                Log.d(TAG, "USB not connected, attempting to scan and connect...");

                // Try to scan for USB devices
                communicationManager.scanForUSBDevices();

                // Give it a moment to scan and connect
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }

                // Check again after scan
                if (communicationManager.isUSBConnected()) {
                    Log.d(TAG, "USB connection established after scan");
                    communicationManager.forceUSBMode();
                } else {
                    Log.w(TAG, "USB still not connected - distance monitoring will use WiFi fallback");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error ensuring USB connection: " + e.getMessage());
            // Continue with whatever connection is available
        }
    }

    private void startRealTimeMonitoring() {
        if (!isRealTimeMonitoringActive) {
            isRealTimeMonitoringActive = true;
            ultrasonicDistanceValue.setText("Starting...");

            // Start the monitoring loop
            distanceUpdateHandler.post(distanceUpdateRunnable);

            Log.d(TAG, "Real-time distance monitoring started");
        }
    }

    private void stopRealTimeMonitoring() {
        if (isRealTimeMonitoringActive) {
            isRealTimeMonitoringActive = false;

            // Stop the monitoring loop
            if (distanceUpdateHandler != null) {
                distanceUpdateHandler.removeCallbacks(distanceUpdateRunnable);
            }

            ultrasonicDistanceValue.setText("-- cm");
            Log.d(TAG, "Real-time distance monitoring stopped");
        }
    }

    // Update distance display when response is received (for Smart Greeting integration)
    public void updateDistanceDisplay(float distance) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                if (distance >= 999.0f) {
                    ultrasonicDistanceValue.setText("Out of range");
                    ultrasonicDistanceValue.setTextColor(0xFFFF0000); // Red color
                } else if (distance <= 30.0f) {
                    // Smart Greeting range - 30cm for handshake
                    ultrasonicDistanceValue.setText(String.format("%.1f cm", distance));
                    ultrasonicDistanceValue.setTextColor(0xFF00FF00); // Green color
                } else {
                    ultrasonicDistanceValue.setText(String.format("%.1f cm", distance));
                    ultrasonicDistanceValue.setTextColor(0xFF888888); // Gray color
                }

                // Update distance display in MainActivity status bar
                updateMainActivityDistanceDisplay(distance);
            });
        }
    }

    /**
     * Update distance display in MainActivity status bar
     */
    private void updateMainActivityDistanceDisplay(float distance) {
        if (getActivity() instanceof MainActivity) {
            MainActivity mainActivity = (MainActivity) getActivity();
            // Check if Smart Greeting is enabled (for now, always true)
            boolean smartGreetingEnabled = true; // This should come from settings
            mainActivity.updateDistanceDisplay(distance, smartGreetingEnabled);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        startRealTimeMonitoring();
    }

    @Override
    public void onPause() {
        super.onPause();
        stopRealTimeMonitoring();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        stopRealTimeMonitoring();


    }

    // CommunicationListener interface implementation
    @Override
    public void onCommandSent(String controllerId, RobotCommand command) {
        // Handle command sent confirmation if needed
    }

    @Override
    public void onResponseReceived(String controllerId, RobotResponse response) {
        // Handle distance responses from ESP32
        if ("sensor".equals(controllerId) && response.getData() != null) {
            Map<String, Object> data = response.getData();

            if (data.containsKey("ultrasonic_distance")) {
                Object distanceObj = data.get("ultrasonic_distance");
                if (distanceObj instanceof Number) {
                    float distance = ((Number) distanceObj).floatValue();
                    updateDistanceDisplay(distance);
                }
            }
        }
    }

    @Override
    public void onConnectionStatusChanged(String controllerId, boolean connected) {
        // Handle connection status changes if needed
    }

    @Override
    public void onError(String controllerId, String error) {
        // Handle communication errors
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                showError("Communication error: " + error);
            });
        }
    }
}
