package com.stemrobo.humanoid.utils;

import com.stemrobo.humanoid.models.Preset;
import com.stemrobo.humanoid.models.PresetAction;
import com.stemrobo.humanoid.models.PresetStep;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Utility class for creating default preset library.
 * Provides pre-built example presets for common robot behaviors.
 */
public class DefaultPresets {
    
    /**
     * Creates a list of default presets to help users get started.
     * @return List of default presets
     */
    public static List<Preset> createDefaultPresets() {
        List<Preset> presets = new ArrayList<>();
        
        presets.add(createGreetingPreset());
        presets.add(createPatrolPreset());
        presets.add(createDancePreset());
        presets.add(createWavePreset());
        presets.add(createDemoPreset());
        
        return presets;
    }
    
    /**
     * Creates a friendly greeting preset.
     */
    private static Preset createGreetingPreset() {
        Preset preset = new Preset();
        preset.setName("Friendly Greeting");
        preset.setDescription("A warm welcome with speech and gestures");
        preset.setCategory(Preset.Category.SOCIAL);
        
        List<PresetStep> steps = new ArrayList<>();
        
        // Step 1: Say hello
        PresetStep step1 = new PresetStep();
        step1.setName("Say Hello");
        step1.setStartTimeMs(0);
        step1.setDurationMs(2000);
        
        PresetAction speechAction = new PresetAction();
        speechAction.setType(PresetAction.ActionType.SPEECH);
        speechAction.setDescription("Greet the person");
        speechAction.setParameters(createSpeechParams("Hello! Welcome to the future of robotics!"));
        
        step1.setActions(Arrays.asList(speechAction));
        steps.add(step1);
        
        // Step 2: Wave gesture
        PresetStep step2 = new PresetStep();
        step2.setName("Wave Hand");
        step2.setStartTimeMs(2500);
        step2.setDurationMs(3000);
        
        PresetAction gestureAction = new PresetAction();
        gestureAction.setType(PresetAction.ActionType.GESTURE);
        gestureAction.setDescription("Friendly wave");
        gestureAction.setParameters(createGestureParams("Wave"));
        
        step2.setActions(Arrays.asList(gestureAction));
        steps.add(step2);
        
        // Step 3: Introduction
        PresetStep step3 = new PresetStep();
        step3.setName("Introduction");
        step3.setStartTimeMs(6000);
        step3.setDurationMs(3000);
        
        PresetAction introAction = new PresetAction();
        introAction.setType(PresetAction.ActionType.SPEECH);
        introAction.setDescription("Introduce the robot");
        introAction.setParameters(createSpeechParams("I am your STEM humanoid robot. How can I help you today?"));
        
        step3.setActions(Arrays.asList(introAction));
        steps.add(step3);
        
        preset.setSteps(steps);
        preset.setCreatedAt(System.currentTimeMillis());
        preset.setUpdatedAt(System.currentTimeMillis());
        
        return preset;
    }
    
    /**
     * Creates a patrol movement preset.
     */
    private static Preset createPatrolPreset() {
        Preset preset = new Preset();
        preset.setName("Security Patrol");
        preset.setDescription("Basic patrol movement pattern");
        preset.setCategory(Preset.Category.MOVEMENT);
        
        List<PresetStep> steps = new ArrayList<>();
        
        // Step 1: Move forward
        PresetStep step1 = new PresetStep();
        step1.setName("Forward Movement");
        step1.setStartTimeMs(0);
        step1.setDurationMs(3000);
        
        PresetAction forwardAction = new PresetAction();
        forwardAction.setType(PresetAction.ActionType.MOVEMENT);
        forwardAction.setDescription("Move forward");
        forwardAction.setParameters(createMovementParams("forward"));
        
        step1.setActions(Arrays.asList(forwardAction));
        steps.add(step1);
        
        // Step 2: Turn right
        PresetStep step2 = new PresetStep();
        step2.setName("Turn Right");
        step2.setStartTimeMs(3500);
        step2.setDurationMs(2000);
        
        PresetAction rightAction = new PresetAction();
        rightAction.setType(PresetAction.ActionType.MOVEMENT);
        rightAction.setDescription("Turn right");
        rightAction.setParameters(createMovementParams("turn_right"));

        step2.setActions(Arrays.asList(rightAction));
        steps.add(step2);

        // Step 3: Move forward again
        PresetStep step3 = new PresetStep();
        step3.setName("Forward Again");
        step3.setStartTimeMs(6000);
        step3.setDurationMs(3000);

        PresetAction forward2Action = new PresetAction();
        forward2Action.setType(PresetAction.ActionType.MOVEMENT);
        forward2Action.setDescription("Continue forward");
        forward2Action.setParameters(createMovementParams("forward"));

        step3.setActions(Arrays.asList(forward2Action));
        steps.add(step3);

        // Step 4: Turn left
        PresetStep step4 = new PresetStep();
        step4.setName("Turn Left");
        step4.setStartTimeMs(9500);
        step4.setDurationMs(2000);

        PresetAction leftAction = new PresetAction();
        leftAction.setType(PresetAction.ActionType.MOVEMENT);
        leftAction.setDescription("Turn left");
        leftAction.setParameters(createMovementParams("turn_left"));

        step4.setActions(Arrays.asList(leftAction));
        steps.add(step4);

        // Step 5: Stop
        PresetStep step5 = new PresetStep();
        step5.setName("Stop");
        step5.setStartTimeMs(12000);
        step5.setDurationMs(1000);

        PresetAction stopAction = new PresetAction();
        stopAction.setType(PresetAction.ActionType.MOVEMENT);
        stopAction.setDescription("Stop movement");
        stopAction.setParameters(createMovementParams("stop"));
        
        step5.setActions(Arrays.asList(stopAction));
        steps.add(step5);
        
        preset.setSteps(steps);
        preset.setCreatedAt(System.currentTimeMillis());
        preset.setUpdatedAt(System.currentTimeMillis());
        
        return preset;
    }



    /**
     * Creates a simple dance preset.
     */
    private static Preset createDancePreset() {
        Preset preset = new Preset();
        preset.setName("Robot Dance");
        preset.setDescription("Fun dance routine with arm movements");
        preset.setCategory(Preset.Category.ENTERTAINMENT);
        
        List<PresetStep> steps = new ArrayList<>();
        
        // Step 1: Announce dance
        PresetStep step1 = new PresetStep();
        step1.setName("Dance Announcement");
        step1.setStartTimeMs(0);
        step1.setDurationMs(2000);
        
        PresetAction announceAction = new PresetAction();
        announceAction.setType(PresetAction.ActionType.SPEECH);
        announceAction.setDescription("Announce dance");
        announceAction.setParameters(createSpeechParams("Let me show you my dance moves!"));

        step1.setActions(Arrays.asList(announceAction));
        steps.add(step1);

        // Step 2: Right hand up
        PresetStep step2 = new PresetStep();
        step2.setName("Right Hand Up");
        step2.setStartTimeMs(2500);
        step2.setDurationMs(1500);

        PresetAction rightUpAction = new PresetAction();
        rightUpAction.setType(PresetAction.ActionType.HAND_CONTROL);
        rightUpAction.setDescription("Raise right hand");
        rightUpAction.setParameters(createHandParams("Right Hand", "Up", "45"));

        step2.setActions(Arrays.asList(rightUpAction));
        steps.add(step2);

        // Step 3: Left hand up
        PresetStep step3 = new PresetStep();
        step3.setName("Left Hand Up");
        step3.setStartTimeMs(4500);
        step3.setDurationMs(1500);

        PresetAction leftUpAction = new PresetAction();
        leftUpAction.setType(PresetAction.ActionType.HAND_CONTROL);
        leftUpAction.setDescription("Raise left hand");
        leftUpAction.setParameters(createHandParams("Left Hand", "Up", "135"));

        step3.setActions(Arrays.asList(leftUpAction));
        steps.add(step3);

        // Step 4: Both hands down
        PresetStep step4 = new PresetStep();
        step4.setName("Hands Down");
        step4.setStartTimeMs(6500);
        step4.setDurationMs(2000);

        PresetAction rightDownAction = new PresetAction();
        rightDownAction.setType(PresetAction.ActionType.HAND_CONTROL);
        rightDownAction.setDescription("Lower right hand");
        rightDownAction.setParameters(createHandParams("Right Hand", "Rest", "0"));

        PresetAction leftDownAction = new PresetAction();
        leftDownAction.setType(PresetAction.ActionType.HAND_CONTROL);
        leftDownAction.setDescription("Lower left hand");
        leftDownAction.setParameters(createHandParams("Left Hand", "Rest", "180"));
        
        step4.setActions(Arrays.asList(rightDownAction, leftDownAction));
        steps.add(step4);
        
        preset.setSteps(steps);
        preset.setCreatedAt(System.currentTimeMillis());
        preset.setUpdatedAt(System.currentTimeMillis());
        
        return preset;
    }

    /**
     * Creates a simple wave preset.
     */
    private static Preset createWavePreset() {
        Preset preset = new Preset();
        preset.setName("Friendly Wave");
        preset.setDescription("Simple wave gesture for greetings");
        preset.setCategory(Preset.Category.SOCIAL);

        List<PresetStep> steps = new ArrayList<>();

        // Step 1: Wave gesture
        PresetStep step1 = new PresetStep();
        step1.setName("Wave");
        step1.setStartTimeMs(0);
        step1.setDurationMs(3000);

        PresetAction waveAction = new PresetAction();
        waveAction.setType(PresetAction.ActionType.GESTURE);
        waveAction.setDescription("Wave hand");
        waveAction.setParameters(createGestureParams("Wave"));

        step1.setActions(Arrays.asList(waveAction));
        steps.add(step1);

        preset.setSteps(steps);
        preset.setCreatedAt(System.currentTimeMillis());
        preset.setUpdatedAt(System.currentTimeMillis());

        return preset;
    }

    /**
     * Creates a comprehensive demo preset.
     */
    private static Preset createDemoPreset() {
        Preset preset = new Preset();
        preset.setName("Full Demo");
        preset.setDescription("Complete demonstration of robot capabilities");
        preset.setCategory(Preset.Category.DEMONSTRATION);

        List<PresetStep> steps = new ArrayList<>();

        // Step 1: Introduction
        PresetStep step1 = new PresetStep();
        step1.setName("Introduction");
        step1.setStartTimeMs(0);
        step1.setDurationMs(3000);

        PresetAction introAction = new PresetAction();
        introAction.setType(PresetAction.ActionType.SPEECH);
        introAction.setDescription("Demo introduction");
        introAction.setParameters(createSpeechParams("Welcome to my demonstration! I will show you my capabilities."));

        step1.setActions(Arrays.asList(introAction));
        steps.add(step1);

        // Step 2: Movement demo
        PresetStep step2 = new PresetStep();
        step2.setName("Movement Demo");
        step2.setStartTimeMs(3500);
        step2.setDurationMs(2000);

        PresetAction moveAction = new PresetAction();
        moveAction.setType(PresetAction.ActionType.MOVEMENT);
        moveAction.setDescription("Show movement");
        moveAction.setParameters(createMovementParams("forward"));

        step2.setActions(Arrays.asList(moveAction));
        steps.add(step2);

        // Step 3: Stop and announce gesture
        PresetStep step3 = new PresetStep();
        step3.setName("Gesture Demo");
        step3.setStartTimeMs(6000);
        step3.setDurationMs(3000);

        PresetAction stopAction = new PresetAction();
        stopAction.setType(PresetAction.ActionType.MOVEMENT);
        stopAction.setDescription("Stop movement");
        stopAction.setParameters(createMovementParams("stop"));

        PresetAction gestureAction = new PresetAction();
        gestureAction.setType(PresetAction.ActionType.GESTURE);
        gestureAction.setDescription("Show gesture");
        gestureAction.setParameters(createGestureParams("Thumbs Up"));

        PresetAction gestureAnnounce = new PresetAction();
        gestureAnnounce.setType(PresetAction.ActionType.SPEECH);
        gestureAnnounce.setDescription("Announce gesture");
        gestureAnnounce.setParameters(createSpeechParams("I can make gestures too!"));

        step3.setActions(Arrays.asList(stopAction, gestureAction, gestureAnnounce));
        steps.add(step3);

        // Step 4: Conclusion
        PresetStep step4 = new PresetStep();
        step4.setName("Conclusion");
        step4.setStartTimeMs(9500);
        step4.setDurationMs(3000);

        PresetAction conclusionAction = new PresetAction();
        conclusionAction.setType(PresetAction.ActionType.SPEECH);
        conclusionAction.setDescription("Demo conclusion");
        conclusionAction.setParameters(createSpeechParams("Thank you for watching my demonstration! I hope you enjoyed it."));

        step4.setActions(Arrays.asList(conclusionAction));
        steps.add(step4);

        preset.setSteps(steps);
        preset.setCreatedAt(System.currentTimeMillis());
        preset.setUpdatedAt(System.currentTimeMillis());

        return preset;
    }

    /**
     * Helper method to create parameter map for actions
     */
    private static Map<String, String> createParams(String... params) {
        Map<String, String> paramMap = new HashMap<>();
        for (int i = 0; i < params.length; i++) {
            paramMap.put("param" + i, params[i]);
        }
        return paramMap;
    }

    /**
     * Helper method to create speech parameter map
     */
    private static Map<String, String> createSpeechParams(String text) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("text", text);
        paramMap.put("voice_gender", "Female");
        paramMap.put("voice_speed", "1.0");
        paramMap.put("voice_pitch", "1.0");
        return paramMap;
    }

    /**
     * Helper method to create speech parameter map with voice options
     */
    private static Map<String, String> createSpeechParams(String text, String gender, String speed, String pitch) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("text", text);
        paramMap.put("voice_gender", gender);
        paramMap.put("voice_speed", speed);
        paramMap.put("voice_pitch", pitch);
        return paramMap;
    }

    /**
     * Helper method to create hand control parameter map
     */
    private static Map<String, String> createHandParams(String handSide, String position, String angle) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("hand_side", handSide);
        paramMap.put("hand_position", position);
        paramMap.put("angle", angle);
        return paramMap;
    }

    /**
     * Helper method to create head movement parameter map
     */
    private static Map<String, String> createHeadParams(String action, String direction, String angle) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("head_action", action);
        paramMap.put("head_direction", direction);
        paramMap.put("head_angle", angle);
        return paramMap;
    }

    /**
     * Helper method to create servo parameter map (legacy support)
     */
    private static Map<String, String> createServoParams(String servo, String angle) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("servo", servo);
        paramMap.put("angle", angle);
        return paramMap;
    }

    /**
     * Helper method to create movement parameter map
     */
    private static Map<String, String> createMovementParams(String direction) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("direction", direction);
        paramMap.put("speed", "Normal");
        paramMap.put("duration", "2.0");
        return paramMap;
    }

    /**
     * Helper method to create movement parameter map with custom settings
     */
    private static Map<String, String> createMovementParams(String direction, String speed, String duration) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("direction", direction);
        paramMap.put("speed", speed);
        paramMap.put("duration", duration);
        return paramMap;
    }

    /**
     * Helper method to create gesture parameter map
     */
    private static Map<String, String> createGestureParams(String gesture) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("gesture", gesture);
        paramMap.put("gesture_intensity", "Normal");
        return paramMap;
    }

    /**
     * Helper method to create gesture parameter map with intensity
     */
    private static Map<String, String> createGestureParams(String gesture, String intensity) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("gesture", gesture);
        paramMap.put("gesture_intensity", intensity);
        return paramMap;
    }
}
